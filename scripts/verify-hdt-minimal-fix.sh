#!/bin/bash

# ═══════════════════════════════════════════════════════════════
# Verify HDT Minimal Overlay Fixes
# Confirms only HDT transparency/stability fixes were applied
# ═══════════════════════════════════════════════════════════════

WINEPREFIX="/home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A"

echo "═══════════════════════════════════════════════════════════════"
echo "Verifying HDT Minimal Overlay Fixes"
echo "═══════════════════════════════════════════════════════════════"
echo

# Check if Wine prefix exists
if [[ ! -d "$WINEPREFIX" ]]; then
    echo "❌ Wine prefix not found: $WINEPREFIX"
    exit 1
fi

echo "✅ Wine prefix found: $WINEPREFIX"
echo

export WINEPREFIX

echo "🔍 Checking HDT overlay fixes..."

# Check .NET Framework hardware acceleration (CRITICAL for transparency)
echo -n "  • .NET Framework HW Acceleration disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

echo -n "  • .NET Framework HW Acceleration disabled (HKLM): "
if wine reg query "HKEY_LOCAL_MACHINE\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

# Check Windows Forms hardware acceleration (prevents UI flickering)
echo -n "  • Windows Forms HW Acceleration disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework\Windows Forms" /v "DisableHardwareAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

# Check WPF graphics hardware acceleration (prevents overlay flickering)
echo -n "  • WPF Graphics HW Acceleration disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Microsoft\Avalon.Graphics" /v "DisableHWAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

# Check .NET 4.x specific settings
echo -n "  • .NET 4.x HW Acceleration disabled (HKCU): "
if wine reg query "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework\v4.0.30319" /v "DisableHWAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

echo -n "  • .NET 4.x HW Acceleration disabled (HKLM): "
if wine reg query "HKEY_LOCAL_MACHINE\Software\Microsoft\.NETFramework\v4.0.30319" /v "DisableHWAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

echo

echo "🔍 Verifying Battle.net compatibility (should be unaffected)..."

# Verify NO problematic DLL overrides exist
echo -n "  • D3D11 DLL override: "
if wine reg query "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "d3d11" 2>/dev/null | grep -q "disabled"; then
    echo "❌ PRESENT (could break Battle.net)"
else
    echo "✅ NOT PRESENT (good)"
fi

echo -n "  • DXGI DLL override: "
if wine reg query "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dxgi" 2>/dev/null | grep -q "disabled"; then
    echo "❌ PRESENT (could break Battle.net)"
else
    echo "✅ NOT PRESENT (good)"
fi

echo -n "  • DWMAPI DLL override: "
if wine reg query "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dwmapi" 2>/dev/null | grep -q "disabled"; then
    echo "❌ PRESENT (could break Battle.net)"
else
    echo "✅ NOT PRESENT (good)"
fi

echo

echo "🔍 Checking Hyprland window rules..."
if grep -q "HDT.*Overlay" ~/.config/hypr/conf/window_rules.conf; then
    echo "✅ HDT overlay window rules found"
else
    echo "❌ HDT overlay window rules missing"
fi

echo

echo "🔍 Checking NVIDIA anti-flicker settings..."
if grep -q "__GL_SYNC_TO_VBLANK" ~/.config/hypr/conf/env_var.conf; then
    echo "✅ NVIDIA VSync settings found"
else
    echo "❌ NVIDIA VSync settings missing"
fi

echo
echo "═══════════════════════════════════════════════════════════════"
echo "Verification Complete!"
echo "═══════════════════════════════════════════════════════════════"
echo

echo "🎯 HDT Overlay Fixes Applied:"
echo "✅ WPF hardware acceleration disabled → Fixes black background"
echo "✅ Windows Forms acceleration disabled → Prevents UI flickering"
echo "✅ .NET Framework acceleration disabled → Ensures transparency"
echo "✅ Hyprland window rules active → Overlay stays on top"
echo "✅ NVIDIA anti-flicker settings → Stable at 175Hz"
echo

echo "🛡️ Battle.net Compatibility Preserved:"
echo "✅ No DLL overrides that could break Battle.net"
echo "✅ Core graphics libraries (D3D11, DXGI) left intact"
echo "✅ Battle.net should continue working normally"
echo

echo "🚀 Next Steps:"
echo "1. Launch Battle.net → Should work without DLL errors"
echo "2. Launch Hearthstone → Set to WINDOWED mode (critical!)"
echo "3. Launch HDT → Configure overlay settings"
echo "4. Test overlay → Should be transparent and stable"
echo
